"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { But<PERSON> } from "@ui/components/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import {
	Form,
	FormControl,
	FormDescription,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@ui/components/form";
import { Input } from "@ui/components/input";
import { Textarea } from "@ui/components/textarea";
import { ArrowLeftIcon } from "lucide-react";
import { useTranslations } from "next-intl";
import { useForm } from "react-hook-form";
import { z } from "zod";
import type { BusinessInfo } from "../utils/google-maps";

const manualBusinessSchema = z.object({
	name: z
		.string()
		.min(1, "Business name is required")
		.max(100, "Business name is too long"),
	address: z
		.string()
		.min(1, "Address is required")
		.max(200, "Address is too long"),
	phone: z.string().optional(),
	website: z
		.string()
		.url("Please enter a valid website URL")
		.optional()
		.or(z.literal("")),
});

type ManualBusinessFormValues = z.infer<typeof manualBusinessSchema>;

interface ManualBusinessFormProps {
	onSubmit: (businessInfo: BusinessInfo) => void;
	onBack: () => void;
	isSubmitting?: boolean;
}

export function ManualBusinessForm({
	onSubmit,
	onBack,
	isSubmitting = false,
}: ManualBusinessFormProps) {
	const t = useTranslations();

	const form = useForm<ManualBusinessFormValues>({
		resolver: zodResolver(manualBusinessSchema),
		defaultValues: {
			name: "",
			address: "",
			phone: "",
			website: "",
		},
	});

	const handleSubmit = (values: ManualBusinessFormValues) => {
		// Convert form values to BusinessInfo format
		const businessInfo: BusinessInfo = {
			placesId: `manual_${Date.now()}`, // Generate a unique ID for manual entries
			googleMapsUrl: "", // No URL for manual entries
			name: values.name,
			address: values.address,
			phone: values.phone || undefined,
			website: values.website || undefined,
			rating: undefined,
			userRatingsTotal: undefined,
			openingHours: [],
			photos: [],
		};

		onSubmit(businessInfo);
	};

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="text-center">
				<h2 className="text-xl font-semibold mb-2">
					Add Your Business Manually
				</h2>
				<p className="text-sm text-muted-foreground">
					Enter your business information manually to create your
					organization
				</p>
			</div>

			{/* Form */}
			<Card>
				<CardHeader>
					<CardTitle className="text-lg">
						Business Information
					</CardTitle>
				</CardHeader>
				<CardContent>
					<Form {...form}>
						<form
							onSubmit={form.handleSubmit(handleSubmit)}
							className="space-y-4"
						>
							{/* Business Name */}
							<FormField
								control={form.control}
								name="name"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Business Name *</FormLabel>
										<FormControl>
											<Input
												{...field}
												placeholder="e.g., The Artisan Coffee Co."
												disabled={isSubmitting}
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							{/* Address */}
							<FormField
								control={form.control}
								name="address"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Address *</FormLabel>
										<FormControl>
											<Textarea
												{...field}
												placeholder="e.g., 123 Main Street, City, State, ZIP"
												rows={2}
												disabled={isSubmitting}
											/>
										</FormControl>
										<FormDescription>
											Enter your complete business address
										</FormDescription>
										<FormMessage />
									</FormItem>
								)}
							/>

							{/* Phone */}
							<FormField
								control={form.control}
								name="phone"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Phone Number</FormLabel>
										<FormControl>
											<Input
												{...field}
												placeholder="e.g., +****************"
												type="tel"
												disabled={isSubmitting}
											/>
										</FormControl>
										<FormDescription>
											Optional - Your business phone
											number
										</FormDescription>
										<FormMessage />
									</FormItem>
								)}
							/>

							{/* Website */}
							<FormField
								control={form.control}
								name="website"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Website</FormLabel>
										<FormControl>
											<Input
												{...field}
												placeholder="e.g., https://yourbusiness.com"
												type="url"
												disabled={isSubmitting}
											/>
										</FormControl>
										<FormDescription>
											Optional - Your business website URL
										</FormDescription>
										<FormMessage />
									</FormItem>
								)}
							/>

							{/* Action Buttons */}
							<div className="flex flex-col sm:flex-row gap-3 pt-4">
								<Button
									type="button"
									variant="outline"
									onClick={onBack}
									disabled={isSubmitting}
									className="flex items-center gap-2"
								>
									<ArrowLeftIcon className="size-4" />
									Back to URL Entry
								</Button>

								<Button
									type="submit"
									disabled={
										isSubmitting || !form.formState.isValid
									}
									loading={isSubmitting}
									className="flex-1"
								>
									Create Business Organization
								</Button>
							</div>
						</form>
					</Form>
				</CardContent>
			</Card>

			{/* Help Text */}
			<div className="text-center">
				<p className="text-xs text-muted-foreground">
					Don't worry, you can always update this information later in
					your organization settings.
				</p>
			</div>
		</div>
	);
}
