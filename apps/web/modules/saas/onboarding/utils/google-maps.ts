/**
 * Utility functions for handling Google Maps URLs and extracting business information
 */

export interface BusinessInfo {
	placesId: string;
	googleMapsUrl: string;
	name?: string;
	address?: string;
	phone?: string;
	website?: string;
	rating?: number;
	userRatingsTotal?: number;
	openingHours?: string[];
	photos?: Array<{
		reference: string;
		width: number;
		height: number;
	}>;
}

/**
 * Extract Places ID from various Google Maps URL formats
 * Supports a comprehensive range of Google Maps URL patterns
 */
export function extractPlacesIdFromUrl(url: string): string | null {
	try {
		// Handle different Google Maps URL formats with expanded support
		const patterns = [
			// Format 1: https://maps.google.com/maps?cid=123456789
			/[?&]cid=(\d+)/,

			// Format 2: https://www.google.com/maps/place/.../@lat,lng,zoom/data=!3m1!4b1!4m6!3m5!1s0x123:0x456
			/1s([^!]+)/,

			// Format 3: Direct place_id parameter
			/[?&]place_id=([A-Za-z0-9_-]+)/,

			// Format 4: /maps/place/ with coordinates and data
			/\/maps\/place\/[^/]+\/@[^/]+\/data=.*?1s([^!]+)/,

			// Format 5: /search/ URLs with place data
			/\/search\/[^/]+\/data=.*?1s([^!]+)/,

			// Format 6: /dir/ URLs (directions) with place data
			/\/dir\/[^/]+\/data=.*?1s([^!]+)/,

			// Format 7: Embedded maps with place_id
			/\/embed\?.*?pb=.*?1s([^!]+)/,

			// Format 8: /local_business/ URLs
			/\/local_business\/[^/]+\/.*?1s([^!]+)/,

			// Format 9: Business profile URLs
			/\/maps\/place\/[^/]+\/data=.*?1s([^!]+)/,
		];

		for (const pattern of patterns) {
			const match = url.match(pattern);
			if (match) {
				// Decode URL-encoded characters
				return decodeURIComponent(match[1]);
			}
		}

		// Handle shortened URLs (maps.app.goo.gl, goo.gl/maps)
		if (url.includes("maps.app.goo.gl") || url.includes("goo.gl/maps")) {
			return generateDemoPlacesId(url);
		}

		return null;
	} catch {
		return null;
	}
}

/**
 * Generate a consistent demo Places ID for URLs that can't be parsed
 * This ensures the same URL always returns the same demo business
 */
function generateDemoPlacesId(url: string): string {
	// Create a hash of the URL to ensure consistency
	let hash = 0;
	for (let i = 0; i < url.length; i++) {
		const char = url.charCodeAt(i);
		hash = (hash << 5) - hash + char;
		hash = hash & hash; // Convert to 32-bit integer
	}

	// Convert to a Places ID-like format
	return `ChIJ${Math.abs(hash).toString(36).padStart(16, "0").substring(0, 16)}`;
}

/**
 * Validate if a URL is a Google Maps URL
 * Supports various Google Maps domains and formats
 */
export function isGoogleMapsUrl(url: string): boolean {
	try {
		const urlObj = new URL(url);
		const hostname = urlObj.hostname.toLowerCase();

		// Support various Google Maps domains
		const validDomains = [
			"maps.google.com",
			"www.google.com",
			"google.com",
			"maps.app.goo.gl",
			"goo.gl",
			"g.page", // Google Business Profile pages
		];

		// Check if hostname matches any valid domain
		const isValidDomain = validDomains.some(
			(domain) => hostname === domain || hostname.endsWith(`.${domain}`),
		);

		// Additional check for Google Maps paths
		const hasValidPath =
			urlObj.pathname.includes("/maps") ||
			urlObj.pathname.includes("/place") ||
			urlObj.pathname.includes("/search") ||
			urlObj.pathname.includes("/dir") ||
			urlObj.pathname.includes("/embed") ||
			urlObj.pathname.includes("/local_business") ||
			hostname.includes("goo.gl") ||
			hostname.includes("g.page");

		return isValidDomain && hasValidPath;
	} catch {
		return false;
	}
}

/**
 * Extract business information from Google Maps URL
 * DEMO VERSION: Returns fake data that simulates real Google Places API response
 */
export async function extractBusinessInfo(
	googleMapsUrl: string,
): Promise<BusinessInfo | null> {
	const placesId = extractPlacesIdFromUrl(googleMapsUrl);

	if (!placesId) {
		return null;
	}

	// Simulate API delay
	await new Promise((resolve) => setTimeout(resolve, 1500));

	// Demo: Generate fake business data based on the Places ID
	const demoBusinesses = [
		{
			name: "The Artisan Coffee Co.",
			address: "1247 Broadway Street, SoHo, New York, NY 10013",
			phone: "+****************",
			website: "https://artisancoffeeco.com",
			rating: 4.7,
			userRatingsTotal: 284,
			openingHours: [
				"Monday: 6:30 AM – 8:00 PM",
				"Tuesday: 6:30 AM – 8:00 PM",
				"Wednesday: 6:30 AM – 8:00 PM",
				"Thursday: 6:30 AM – 8:00 PM",
				"Friday: 6:30 AM – 9:00 PM",
				"Saturday: 7:00 AM – 9:00 PM",
				"Sunday: 7:00 AM – 7:00 PM",
			],
		},
		{
			name: "Nonna's Italian Kitchen",
			address:
				"892 Columbus Avenue, North Beach, San Francisco, CA 94133",
			phone: "+****************",
			website: "https://nonnasitaliankitchen.com",
			rating: 4.9,
			userRatingsTotal: 567,
			openingHours: [
				"Monday: Closed",
				"Tuesday: 5:00 PM – 10:30 PM",
				"Wednesday: 5:00 PM – 10:30 PM",
				"Thursday: 5:00 PM – 10:30 PM",
				"Friday: 5:00 PM – 11:00 PM",
				"Saturday: 4:30 PM – 11:00 PM",
				"Sunday: 4:30 PM – 9:30 PM",
			],
		},
		{
			name: "Digital Solutions Hub",
			address: "3401 Hillview Avenue, Building C, Palo Alto, CA 94304",
			phone: "+****************",
			website: "https://digitalsolutionshub.com",
			rating: 4.4,
			userRatingsTotal: 156,
			openingHours: [
				"Monday: 8:00 AM – 7:00 PM",
				"Tuesday: 8:00 AM – 7:00 PM",
				"Wednesday: 8:00 AM – 7:00 PM",
				"Thursday: 8:00 AM – 7:00 PM",
				"Friday: 8:00 AM – 6:00 PM",
				"Saturday: 9:00 AM – 5:00 PM",
				"Sunday: Closed",
			],
		},
		{
			name: "Bloom & Blossom Florist",
			address: "567 Rose Garden Lane, Beverly Hills, CA 90210",
			phone: "+****************",
			website: "https://bloomandblossom.com",
			rating: 4.8,
			userRatingsTotal: 203,
			openingHours: [
				"Monday: 9:00 AM – 6:00 PM",
				"Tuesday: 9:00 AM – 6:00 PM",
				"Wednesday: 9:00 AM – 6:00 PM",
				"Thursday: 9:00 AM – 6:00 PM",
				"Friday: 9:00 AM – 7:00 PM",
				"Saturday: 8:00 AM – 7:00 PM",
				"Sunday: 10:00 AM – 5:00 PM",
			],
		},
		{
			name: "Metro Fitness Center",
			address: "1890 Fitness Boulevard, Downtown, Chicago, IL 60601",
			phone: "+****************",
			website: "https://metrofitnesscenter.com",
			rating: 4.3,
			userRatingsTotal: 412,
			openingHours: [
				"Monday: 5:00 AM – 11:00 PM",
				"Tuesday: 5:00 AM – 11:00 PM",
				"Wednesday: 5:00 AM – 11:00 PM",
				"Thursday: 5:00 AM – 11:00 PM",
				"Friday: 5:00 AM – 10:00 PM",
				"Saturday: 6:00 AM – 10:00 PM",
				"Sunday: 7:00 AM – 9:00 PM",
			],
		},
	];

	// Select demo business based on Places ID hash
	const businessIndex =
		Math.abs(
			placesId.split("").reduce((acc, b) => {
				const newAcc = (acc << 5) - acc + b.charCodeAt(0);
				return newAcc & newAcc;
			}, 0),
		) % demoBusinesses.length;

	const demoBusiness = demoBusinesses[businessIndex];

	return {
		placesId,
		googleMapsUrl,
		name: demoBusiness.name,
		address: demoBusiness.address,
		phone: demoBusiness.phone,
		website: demoBusiness.website,
		rating: demoBusiness.rating,
		userRatingsTotal: demoBusiness.userRatingsTotal,
		openingHours: demoBusiness.openingHours,
		photos: [
			{
				reference: "demo_photo_1",
				width: 400,
				height: 300,
			},
			{
				reference: "demo_photo_2",
				width: 400,
				height: 300,
			},
		],
	};
}

/**
 * Get Google Places API URL for fetching business details
 * This is a helper function for when you implement the actual API integration
 */
export function getPlacesApiUrl(placesId: string, apiKey: string): string {
	const fields = [
		"name",
		"formatted_address",
		"formatted_phone_number",
		"website",
		"rating",
		"user_ratings_total",
		"opening_hours",
		"photos",
	].join(",");

	return `https://maps.googleapis.com/maps/api/place/details/json?place_id=${placesId}&fields=${fields}&key=${apiKey}`;
}
