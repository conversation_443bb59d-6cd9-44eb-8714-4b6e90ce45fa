"use client";

import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { SettingsItem } from "@saas/shared/components/SettingsItem";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import { Textarea } from "@ui/components/textarea";
import { SaveIcon } from "lucide-react";
import { useEffect, useState } from "react";
import { WebsiteService } from "../lib/website-service";
import type { GeneratedWebsite } from "../utils/website-generator";

export function WebsiteContentSettings() {
	const { activeOrganization } = useActiveOrganization();
	const [website, setWebsite] = useState<GeneratedWebsite | null>(null);
	const [businessInfo, setBusinessInfo] = useState<any>(null);
	const [saving, setSaving] = useState(false);
	const [loading, setLoading] = useState(true);

	useEffect(() => {
		if (activeOrganization) {
			loadWebsite();
		}
	}, [activeOrganization]);

	const loadWebsite = async () => {
		if (!activeOrganization) {
			return;
		}

		try {
			setLoading(true);
			const websiteData = await WebsiteService.getWebsiteByOrganization(
				activeOrganization.id,
			);
			if (websiteData) {
				setWebsite(websiteData);
				setBusinessInfo(websiteData.businessInfo);
			}
		} catch (err) {
			console.error("Error loading website:", err);
		} finally {
			setLoading(false);
		}
	};

	const handleSave = async () => {
		if (!website || !businessInfo) {
			return;
		}

		setSaving(true);
		try {
			// Note: This would need to be implemented in WebsiteService
			// const updatedWebsite = await WebsiteService.updateBusinessInfo(
			// 	website.id,
			// 	businessInfo,
			// );
			console.log("Saving business info:", businessInfo);
		} catch (error) {
			console.error("Failed to update business info:", error);
		} finally {
			setSaving(false);
		}
	};

	if (loading || !website || !businessInfo) {
		return <div>Loading...</div>;
	}

	return (
		<SettingsItem
			title="Business Information"
			description="Update your business details displayed on the website"
		>
			<div className="space-y-4">
				<div className="space-y-2">
					<Label htmlFor="businessName">Business Name</Label>
					<Input
						id="businessName"
						value={businessInfo.name || ""}
						onChange={(e) =>
							setBusinessInfo((prev: any) => ({
								...prev,
								name: e.target.value,
							}))
						}
					/>
				</div>
				<div className="space-y-2">
					<Label htmlFor="businessAddress">Address</Label>
					<Textarea
						id="businessAddress"
						value={businessInfo.address || ""}
						onChange={(e) =>
							setBusinessInfo((prev: any) => ({
								...prev,
								address: e.target.value,
							}))
						}
						rows={2}
					/>
				</div>
				<div className="grid grid-cols-2 gap-4">
					<div className="space-y-2">
						<Label htmlFor="businessPhone">Phone</Label>
						<Input
							id="businessPhone"
							value={businessInfo.phone || ""}
							onChange={(e) =>
								setBusinessInfo((prev: any) => ({
									...prev,
									phone: e.target.value,
								}))
							}
						/>
					</div>
					<div className="space-y-2">
						<Label htmlFor="businessWebsite">Website</Label>
						<Input
							id="businessWebsite"
							value={businessInfo.website || ""}
							onChange={(e) =>
								setBusinessInfo((prev: any) => ({
									...prev,
									website: e.target.value,
								}))
							}
						/>
					</div>
				</div>
				<Button onClick={handleSave} loading={saving}>
					<SaveIcon className="size-4 mr-2" />
					Save Business Information
				</Button>
			</div>
		</SettingsItem>
	);
}
