"use client";

import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { Badge } from "@ui/components/badge";
import { Button } from "@ui/components/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@ui/components/card";
import {
	AlertCircleIcon,
	ExternalLinkIcon,
	GlobeIcon,
	LoaderIcon,
	SettingsIcon,
} from "lucide-react";
import Link from "next/link";
import { useParams } from "next/navigation";
import { useEffect, useState } from "react";
import {
	getTemplateInfo,
	getWebsiteByOrganization,
} from "../lib/website-service";
import type { GeneratedWebsite } from "../utils/website-generator";
import { WebsiteAnalyticsCharts } from "./WebsiteAnalyticsCharts";

export function WebsiteDashboardContent() {
	const { activeOrganization } = useActiveOrganization();
	const params = useParams();
	const organizationSlug = params.organizationSlug as string;
	const [website, setWebsite] = useState<GeneratedWebsite | null>(null);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);

	useEffect(() => {
		if (activeOrganization) {
			loadWebsite();
		}
	}, [activeOrganization]);

	const loadWebsite = async () => {
		if (!activeOrganization) {
			return;
		}

		try {
			setLoading(true);
			const websiteData = await getWebsiteByOrganization(
				activeOrganization.id,
			);
			setWebsite(websiteData);
			setError(null);
		} catch (err) {
			setError("Failed to load website data");
			console.error("Error loading website:", err);
		} finally {
			setLoading(false);
		}
	};

	if (loading) {
		return (
			<div className="flex items-center justify-center py-12">
				<div className="flex items-center gap-3">
					<LoaderIcon className="size-5 animate-spin" />
					<span className="text-muted-foreground">
						Loading website data...
					</span>
				</div>
			</div>
		);
	}

	if (error) {
		return (
			<div className="flex items-center justify-center py-12">
				<div className="flex items-center gap-3 text-destructive">
					<AlertCircleIcon className="size-5" />
					<span>{error}</span>
				</div>
			</div>
		);
	}

	if (!website) {
		return (
			<div className="text-center py-12">
				<div className="max-w-md mx-auto">
					<GlobeIcon className="size-12 mx-auto text-muted-foreground mb-4" />
					<h3 className="text-lg font-semibold mb-2">
						No Website Found
					</h3>
					<p className="text-muted-foreground mb-4">
						It looks like your website hasn't been generated yet.
						This usually happens automatically during onboarding.
					</p>
					<Button onClick={loadWebsite}>Refresh</Button>
				</div>
			</div>
		);
	}

	const { config, status, url } = website;
	const templateInfo = getTemplateInfo(config.template);

	return (
		<div className="space-y-6">
			{/* Website Status Card */}
			<Card>
				<CardHeader>
					<div className="flex items-center justify-between">
						<div>
							<CardTitle className="flex items-center gap-2">
								<GlobeIcon className="size-5" />
								Your Website
							</CardTitle>
							<CardDescription>
								{templateInfo.name} template •{" "}
								{templateInfo.description}
							</CardDescription>
						</div>
						<Badge
							status={
								status === "active"
									? "success"
									: status === "generating"
										? "info"
										: "error"
							}
							className="capitalize"
						>
							{status === "generating" && (
								<LoaderIcon className="size-3 mr-1 animate-spin" />
							)}
							{status}
						</Badge>
					</div>
				</CardHeader>
				<CardContent>
					<div className="flex items-center justify-between">
						<div className="space-y-1">
							<p className="font-medium">{url}</p>
							<p className="text-sm text-muted-foreground">
								Last updated:{" "}
								{new Date(
									website.updatedAt,
								).toLocaleDateString()}
							</p>
						</div>
						<div className="flex gap-2">
							<Button variant="outline" size="sm" asChild>
								<Link
									href={`/app/${organizationSlug}/website/design`}
								>
									<SettingsIcon className="size-4 mr-2" />
									Settings
								</Link>
							</Button>
							<Button size="sm" asChild>
								<a
									href={url}
									target="_blank"
									rel="noopener noreferrer"
								>
									<ExternalLinkIcon className="size-4 mr-2" />
									View Site
								</a>
							</Button>
						</div>
					</div>
				</CardContent>
			</Card>

			{/* Enhanced Analytics Charts */}
			<WebsiteAnalyticsCharts website={website} />
		</div>
	);
}
