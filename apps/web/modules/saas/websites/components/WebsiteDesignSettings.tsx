"use client";

import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { SettingsItem } from "@saas/shared/components/SettingsItem";
import { Badge } from "@ui/components/badge";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import { SaveIcon } from "lucide-react";
import { useEffect, useState } from "react";
import {
	getTemplateInfo,
	getWebsiteByOrganization,
	updateWebsiteConfig,
} from "../lib/website-service";
import type { GeneratedWebsite } from "../utils/website-generator";

export function WebsiteDesignSettings() {
	const { activeOrganization } = useActiveOrganization();
	const [website, setWebsite] = useState<GeneratedWebsite | null>(null);
	const [config, setConfig] = useState<any>(null);
	const [saving, setSaving] = useState(false);
	const [loading, setLoading] = useState(true);

	useEffect(() => {
		if (activeOrganization) {
			loadWebsite();
		}
	}, [activeOrganization]);

	const loadWebsite = async () => {
		if (!activeOrganization) {
			return;
		}

		try {
			setLoading(true);
			const websiteData = await getWebsiteByOrganization(
				activeOrganization.id,
			);
			if (websiteData) {
				setWebsite(websiteData);
				setConfig(websiteData.config);
			}
		} catch (err) {
			console.error("Error loading website:", err);
		} finally {
			setLoading(false);
		}
	};

	const handleSave = async () => {
		if (!website || !config) {
			return;
		}

		setSaving(true);
		try {
			const updatedWebsite = await updateWebsiteConfig(
				website.id,
				config,
			);
			if (updatedWebsite) {
				setWebsite(updatedWebsite);
			}
		} catch (error) {
			console.error("Failed to update website:", error);
		} finally {
			setSaving(false);
		}
	};

	if (loading || !website || !config) {
		return <div>Loading...</div>;
	}

	const templateInfo = getTemplateInfo(config.template);

	return (
		<>
			<SettingsItem
				title="Template"
				description="Your website is using the selected template"
			>
				<div className="flex items-center gap-4 p-4 border rounded-lg">
					<div
						className="w-12 h-12 rounded-lg"
						style={{
							backgroundColor: templateInfo.color,
						}}
					/>
					<div className="flex-1">
						<h4 className="font-medium">{templateInfo.name}</h4>
						<p className="text-sm text-muted-foreground">
							{templateInfo.description}
						</p>
					</div>
					<Badge status="success">Active</Badge>
				</div>
			</SettingsItem>

			<SettingsItem
				title="Colors"
				description="Customize your website's color scheme"
			>
				<div className="space-y-4">
					<div className="grid grid-cols-2 gap-4">
						<div className="space-y-2">
							<Label htmlFor="primaryColor">Primary Color</Label>
							<div className="flex items-center gap-2">
								<Input
									id="primaryColor"
									type="color"
									value={config.primaryColor}
									onChange={(e) =>
										setConfig((prev: any) => ({
											...prev,
											primaryColor: e.target.value,
										}))
									}
									className="w-16 h-10 p-1"
								/>
								<Input
									value={config.primaryColor}
									onChange={(e) =>
										setConfig((prev: any) => ({
											...prev,
											primaryColor: e.target.value,
										}))
									}
									placeholder="#2563eb"
								/>
							</div>
						</div>
						<div className="space-y-2">
							<Label htmlFor="secondaryColor">
								Secondary Color
							</Label>
							<div className="flex items-center gap-2">
								<Input
									id="secondaryColor"
									type="color"
									value={config.secondaryColor}
									onChange={(e) =>
										setConfig((prev: any) => ({
											...prev,
											secondaryColor: e.target.value,
										}))
									}
									className="w-16 h-10 p-1"
								/>
								<Input
									value={config.secondaryColor}
									onChange={(e) =>
										setConfig((prev: any) => ({
											...prev,
											secondaryColor: e.target.value,
										}))
									}
									placeholder="#64748b"
								/>
							</div>
						</div>
					</div>
					<Button onClick={handleSave} loading={saving}>
						<SaveIcon className="size-4 mr-2" />
						Save Colors
					</Button>
				</div>
			</SettingsItem>

			<SettingsItem
				title="Layout"
				description="Choose your website layout style"
			>
				<div className="space-y-4">
					<Select
						value={config.layout}
						onValueChange={(
							value: "modern" | "classic" | "minimal",
						) =>
							setConfig((prev: any) => ({
								...prev,
								layout: value,
							}))
						}
					>
						<SelectTrigger>
							<SelectValue />
						</SelectTrigger>
						<SelectContent>
							<SelectItem value="modern">Modern</SelectItem>
							<SelectItem value="classic">Classic</SelectItem>
							<SelectItem value="minimal">Minimal</SelectItem>
						</SelectContent>
					</Select>
					<Button onClick={handleSave} loading={saving}>
						<SaveIcon className="size-4 mr-2" />
						Save Layout
					</Button>
				</div>
			</SettingsItem>
		</>
	);
}
