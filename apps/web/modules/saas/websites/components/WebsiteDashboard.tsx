"use client";

import { useParams, useRouter } from "next/navigation";
import { useEffect } from "react";

export function WebsiteDashboard() {
	const router = useRouter();
	const params = useParams();
	const organizationSlug = params.organizationSlug as string;

	useEffect(() => {
		// Redirect to the new website dashboard
		if (organizationSlug) {
			router.replace(`/app/${organizationSlug}/website`);
		}
	}, [router, organizationSlug]);

	return null;
}
