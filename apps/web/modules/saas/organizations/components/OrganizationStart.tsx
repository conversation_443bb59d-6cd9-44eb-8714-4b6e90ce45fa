"use client";
import { useParams, useRouter } from "next/navigation";
import { useEffect } from "react";

export default function OrganizationStart() {
	const router = useRouter();
	const params = useParams();
	const organizationSlug = params.organizationSlug as string;

	useEffect(() => {
		// Redirect to the new website dashboard
		router.replace(`/app/${organizationSlug}/website`);
	}, [router, organizationSlug]);

	return null;
}
