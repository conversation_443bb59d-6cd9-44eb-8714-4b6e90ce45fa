import { getActiveOrganization } from "@saas/auth/lib/server";
import { SettingsList } from "@saas/shared/components/SettingsList";
import { WebsiteContentSettings } from "@saas/websites/components/WebsiteContentSettings";
import { notFound } from "next/navigation";

export default async function WebsiteContentPage({
	params,
}: {
	params: Promise<{ organizationSlug: string }>;
}) {
	const { organizationSlug } = await params;
	const organization = await getActiveOrganization(organizationSlug);

	if (!organization) {
		return notFound();
	}

	return (
		<SettingsList>
			<WebsiteContentSettings />
		</SettingsList>
	);
}
