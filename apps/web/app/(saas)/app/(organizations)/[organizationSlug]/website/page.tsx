import { getActiveOrganization } from "@saas/auth/lib/server";
import { WebsiteDashboardContent } from "@saas/websites/components/WebsiteDashboardContent";
import { notFound } from "next/navigation";

export default async function WebsitePage({
	params,
}: {
	params: Promise<{ organizationSlug: string }>;
}) {
	const { organizationSlug } = await params;
	const organization = await getActiveOrganization(organizationSlug);

	if (!organization) {
		return notFound();
	}

	return <WebsiteDashboardContent />;
}
