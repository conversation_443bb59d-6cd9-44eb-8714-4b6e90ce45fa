import { getActiveOrganization } from "@saas/auth/lib/server";
import { SettingsList } from "@saas/shared/components/SettingsList";
import { WebsiteFeaturesSettings } from "@saas/websites/components/WebsiteFeaturesSettings";
import { notFound } from "next/navigation";

export default async function WebsiteFeaturesPage({
	params,
}: {
	params: Promise<{ organizationSlug: string }>;
}) {
	const { organizationSlug } = await params;
	const organization = await getActiveOrganization(organizationSlug);

	if (!organization) {
		return notFound();
	}

	return (
		<SettingsList>
			<WebsiteFeaturesSettings />
		</SettingsList>
	);
}
