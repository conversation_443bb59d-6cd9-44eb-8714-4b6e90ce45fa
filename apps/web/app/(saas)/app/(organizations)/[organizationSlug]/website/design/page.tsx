import { getActiveOrganization } from "@saas/auth/lib/server";
import { SettingsList } from "@saas/shared/components/SettingsList";
import { WebsiteDesignSettings } from "@saas/websites/components/WebsiteDesignSettings";
import { notFound } from "next/navigation";

export default async function WebsiteDesignPage({
	params,
}: {
	params: Promise<{ organizationSlug: string }>;
}) {
	const { organizationSlug } = await params;
	const organization = await getActiveOrganization(organizationSlug);

	if (!organization) {
		return notFound();
	}

	return (
		<SettingsList>
			<WebsiteDesignSettings />
		</SettingsList>
	);
}
