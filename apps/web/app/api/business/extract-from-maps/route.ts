import {
	extractBusinessInfo,
	extractPlacesIdFromUrl,
	isGoogleMapsUrl,
} from "@saas/onboarding/utils/google-maps";
import { type NextRequest, NextResponse } from "next/server";

// Error types for better error handling
enum ErrorType {
	MISSING_URL = "MISSING_URL",
	INVALID_URL_FORMAT = "INVALID_URL_FORMAT",
	NOT_GOOGLE_MAPS_URL = "NOT_GOOGLE_MAPS_URL",
	UNSUPPORTED_URL_FORMAT = "UNSUPPORTED_URL_FORMAT",
	PLACES_ID_NOT_FOUND = "PLACES_ID_NOT_FOUND",
	BUSINESS_INFO_EXTRACTION_FAILED = "BUSINESS_INFO_EXTRACTION_FAILED",
	INTERNAL_ERROR = "INTERNAL_ERROR",
}

// Error messages with specific guidance
const ERROR_MESSAGES = {
	[ErrorType.MISSING_URL]: {
		message: "Google Maps URL is required",
		suggestion: "Please provide a valid Google Maps business URL",
	},
	[ErrorType.INVALID_URL_FORMAT]: {
		message: "Invalid URL format",
		suggestion:
			"Please check that you've entered a complete URL starting with https://",
	},
	[ErrorType.NOT_GOOGLE_MAPS_URL]: {
		message: "This doesn't appear to be a Google Maps URL",
		suggestion:
			"Please use a URL from maps.google.com, google.com/maps, or a Google Business profile",
	},
	[ErrorType.UNSUPPORTED_URL_FORMAT]: {
		message: "This Google Maps URL format is not supported",
		suggestion:
			"Try copying the URL directly from the business page on Google Maps",
	},
	[ErrorType.PLACES_ID_NOT_FOUND]: {
		message: "Could not find business information in this URL",
		suggestion:
			"Make sure the URL is for a specific business, not a general location or search",
	},
	[ErrorType.BUSINESS_INFO_EXTRACTION_FAILED]: {
		message: "Failed to extract business information",
		suggestion:
			"The business might not be publicly available or the URL might be incorrect",
	},
	[ErrorType.INTERNAL_ERROR]: {
		message: "An unexpected error occurred",
		suggestion:
			"Please try again in a moment or contact support if the problem persists",
	},
};

export async function POST(request: NextRequest) {
	try {
		const { googleMapsUrl } = await request.json();

		// Validate input presence
		if (!googleMapsUrl || typeof googleMapsUrl !== "string") {
			const error = ERROR_MESSAGES[ErrorType.MISSING_URL];
			return NextResponse.json(
				{
					error: error.message,
					suggestion: error.suggestion,
					errorType: ErrorType.MISSING_URL,
				},
				{ status: 400 },
			);
		}

		// Validate URL format
		try {
			new URL(googleMapsUrl);
		} catch {
			const error = ERROR_MESSAGES[ErrorType.INVALID_URL_FORMAT];
			return NextResponse.json(
				{
					error: error.message,
					suggestion: error.suggestion,
					errorType: ErrorType.INVALID_URL_FORMAT,
				},
				{ status: 400 },
			);
		}

		// Validate Google Maps URL
		if (!isGoogleMapsUrl(googleMapsUrl)) {
			const error = ERROR_MESSAGES[ErrorType.NOT_GOOGLE_MAPS_URL];
			return NextResponse.json(
				{
					error: error.message,
					suggestion: error.suggestion,
					errorType: ErrorType.NOT_GOOGLE_MAPS_URL,
				},
				{ status: 400 },
			);
		}

		// Check if we can extract Places ID
		const placesId = extractPlacesIdFromUrl(googleMapsUrl);
		if (!placesId) {
			const error = ERROR_MESSAGES[ErrorType.PLACES_ID_NOT_FOUND];
			return NextResponse.json(
				{
					error: error.message,
					suggestion: error.suggestion,
					errorType: ErrorType.PLACES_ID_NOT_FOUND,
				},
				{ status: 400 },
			);
		}

		// Extract business information using demo data
		const businessInfo = await extractBusinessInfo(googleMapsUrl);
		if (!businessInfo) {
			const error =
				ERROR_MESSAGES[ErrorType.BUSINESS_INFO_EXTRACTION_FAILED];
			return NextResponse.json(
				{
					error: error.message,
					suggestion: error.suggestion,
					errorType: ErrorType.BUSINESS_INFO_EXTRACTION_FAILED,
				},
				{ status: 400 },
			);
		}

		// TODO: Uncomment and implement when you have Google Maps API key
		/*
		const apiKey = process.env.GOOGLE_MAPS_API_KEY;
		if (!apiKey) {
			return NextResponse.json(
				{ error: "Google Maps API key not configured" },
				{ status: 500 }
			);
		}

		// Fetch business details from Google Places API
		const placesApiUrl = `https://maps.googleapis.com/maps/api/place/details/json?place_id=${placesId}&fields=name,formatted_address,formatted_phone_number,website,rating,user_ratings_total,opening_hours,photos&key=${apiKey}`;
		
		const response = await fetch(placesApiUrl);
		const data = await response.json();

		if (data.status !== "OK") {
			return NextResponse.json(
				{ error: "Failed to fetch business information from Google Places API" },
				{ status: 400 }
			);
		}

		const place = data.result;
		const businessInfo = {
			placesId,
			googleMapsUrl,
			name: place.name,
			address: place.formatted_address,
			phone: place.formatted_phone_number,
			website: place.website,
			rating: place.rating,
			userRatingsTotal: place.user_ratings_total,
			openingHours: place.opening_hours?.weekday_text || [],
			photos: place.photos?.map((photo: any) => ({
				reference: photo.photo_reference,
				width: photo.width,
				height: photo.height,
			})) || [],
		};
		*/

		return NextResponse.json({
			businessInfo: {
				...businessInfo,
				extractedAt: new Date().toISOString(),
				source: "google_maps_demo",
			},
		});
	} catch (error) {
		console.error("Error extracting business information:", error);
		const errorInfo = ERROR_MESSAGES[ErrorType.INTERNAL_ERROR];
		return NextResponse.json(
			{
				error: errorInfo.message,
				suggestion: errorInfo.suggestion,
				errorType: ErrorType.INTERNAL_ERROR,
			},
			{ status: 500 },
		);
	}
}
